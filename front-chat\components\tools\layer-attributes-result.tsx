"use client";

import React from "react";
import { Database, Info, Hash, Type, Calendar, MapPin, Table, Eye } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  HoverCard,
  HoverCardContent,
  HoverCardTrigger,
} from "@/components/ui/hover-card";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { cn } from "@/lib/utils";
import { componentStyles } from "@/lib/design-tokens";
import { UseMapReturn } from "@geon-map/odf";
import { CompactResultTrigger } from "./common/compact-result-trigger";
import { getToolDisplayInfo } from "../annotations";

interface LayerColumn {
  name: string;
  description: string;
  type: string;
  editable: boolean;
  required: boolean;
  minValue: number | null;
  maxValue: number | null;
  groupCode: string | null;
  groupName: string | null;
}

interface LayerAttributesResponse {
  columns: LayerColumn[];
  data: Record<string, any>[];
  error?: string;
  status?: string;
}

interface LayerAttributesResultProps {
  content: LayerAttributesResponse | string;
  className?: string;
  mapState?: UseMapReturn;
}

const getAttributeTypeColor = (type: string) => {
  const colors = {
    'string': 'bg-blue-100/80 text-blue-700 border-blue-200/60',
    'text': 'bg-blue-100/80 text-blue-700 border-blue-200/60',
    'varchar': 'bg-blue-100/80 text-blue-700 border-blue-200/60',
    'integer': 'bg-emerald-100/80 text-emerald-700 border-emerald-200/60',
    'int': 'bg-emerald-100/80 text-emerald-700 border-emerald-200/60',
    'number': 'bg-emerald-100/80 text-emerald-700 border-emerald-200/60',
    'double': 'bg-emerald-100/80 text-emerald-700 border-emerald-200/60',
    'float': 'bg-emerald-100/80 text-emerald-700 border-emerald-200/60',
    'date': 'bg-purple-100/80 text-purple-700 border-purple-200/60',
    'datetime': 'bg-purple-100/80 text-purple-700 border-purple-200/60',
    'timestamp': 'bg-purple-100/80 text-purple-700 border-purple-200/60',
    'boolean': 'bg-orange-100/80 text-orange-700 border-orange-200/60',
    'geometry': 'bg-pink-100/80 text-pink-700 border-pink-200/60',
    'point': 'bg-pink-100/80 text-pink-700 border-pink-200/60',
    'polygon': 'bg-pink-100/80 text-pink-700 border-pink-200/60',
    'linestring': 'bg-pink-100/80 text-pink-700 border-pink-200/60',
    'default': 'bg-neutral-100/80 text-neutral-700 border-neutral-200/60'
  };
  return colors[type.toLowerCase() as keyof typeof colors] || colors.default;
};

const getAttributeIcon = (type: string) => {
  const lowerType = type.toLowerCase();
  if (lowerType.includes('string') || lowerType.includes('text') || lowerType.includes('varchar')) {
    return <Type className="h-3 w-3" />;
  }
  if (lowerType.includes('int') || lowerType.includes('number') || lowerType.includes('double') || lowerType.includes('float')) {
    return <Hash className="h-3 w-3" />;
  }
  if (lowerType.includes('date') || lowerType.includes('time')) {
    return <Calendar className="h-3 w-3" />;
  }
  if (lowerType.includes('geometry') || lowerType.includes('point') || lowerType.includes('polygon') || lowerType.includes('line')) {
    return <MapPin className="h-3 w-3" />;
  }
  return <Database className="h-3 w-3" />;
};

export function LayerAttributesResult({ content, className }: LayerAttributesResultProps) {
  let result: LayerAttributesResponse;

  try {
    result = typeof content === 'string' ? JSON.parse(content) : content;
  } catch (e) {
    return null;
  }

  console.log("LayerAttributesResult result:", result);

  if (result.error || result.status === 'error') {
    return (
      <div className={cn(componentStyles.card.error, "p-3", className)}>
        <div className="flex items-center gap-2">
          <div className={cn(componentStyles.iconContainer.sm, "bg-red-100/80 text-red-600 border border-red-200/60")}>
            <Info className="h-3 w-3" />
          </div>
          <div>
            <p className="font-medium text-red-900 text-sm">레이어 속성을 가져올 수 없습니다</p>
            <p className="text-xs text-red-700">
              {result.error || "레이어가 존재하지 않거나 접근할 수 없습니다"}
            </p>
          </div>
        </div>
      </div>
    );
  }

  if (!result.columns || result.columns.length === 0) {
    return (
      <div className={cn(componentStyles.card.warning, "p-3", className)}>
        <div className="flex items-center gap-2">
          <div className={cn(componentStyles.iconContainer.sm, "bg-neutral-100/80 text-neutral-600 border border-neutral-200/60")}>
            <Database className="h-3 w-3" />
          </div>
          <div>
            <p className="font-medium text-neutral-900 text-sm">속성 정보가 없습니다</p>
            <p className="text-xs text-neutral-600">이 레이어에는 조회 가능한 속성이 없습니다</p>
          </div>
        </div>
      </div>
    );
  }

  const toolInfo = getToolDisplayInfo("getLayerAttributes");

  return (
    <CompactResultTrigger
      icon={toolInfo.icon}
      title={toolInfo.label}
      state="result"
      className={className}
      titleExtra={
        <Badge variant="outline" className="text-xs py-0 border bg-blue-100/80 text-blue-700 border-blue-200/60">
          {result.columns.length}개 속성
        </Badge>
      }
    >
      <div className="space-y-3">
        <div className="flex items-center gap-2">
          {/* 데이터 미리보기 Popover */}
          {result.data.length > 0 && (
            <Popover>
              <PopoverTrigger asChild>
                <Button variant="outline" size="sm" className="h-6 bg-green-100/80 text-green-700 border-green-200/60 hover:bg-green-200/80">
                  <Info className="h-3 w-3" />
                  <span className="text-xs">데이터 미리보기</span>
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-[600px] max-h-[500px] overflow-hidden" side="bottom" align="end">
                <div className="space-y-3">
                  <div className="flex items-center gap-2 pb-2 border-b border-neutral-200">
                    <div className={cn(componentStyles.iconContainer.sm, "bg-green-100/80 text-green-600 border border-green-200/60")}>
                      <Table className="h-3 w-3" />
                    </div>
                    <span className="font-medium text-neutral-900 text-sm">데이터 미리보기</span>
                    <Badge variant="outline" className="bg-green-100/80 text-green-700 border-green-200/60 text-xs">
                      {result.data.length}개 행
                    </Badge>
                  </div>

                  {/* 반응형 카드 형태로 데이터 표시 - 모바일 친화적 */}
                  <div className="block md:hidden">
                    <div className="space-y-3 max-h-80 overflow-y-auto">
                      {result.data.slice(0, 3).map((row, rowIndex) => (
                        <Card key={rowIndex} className="border border-neutral-200/50">
                          <CardContent className="p-3">
                            <div className="space-y-2">
                              <div className="flex items-center justify-between mb-2">
                                <span className="text-xs font-medium text-neutral-500">데이터 #{rowIndex + 1}</span>
                              </div>
                              {result.columns.slice(0, 4).map((column, colIndex) => {
                                const cellValue = row[column.name];
                                const displayValue = cellValue !== null && cellValue !== undefined ? String(cellValue) : '-';

                                return (
                                  <div key={colIndex} className="flex justify-between items-start gap-2">
                                    <div className="flex items-center gap-1 min-w-0 flex-1">
                                      {getAttributeIcon(column.type)}
                                      <span className="text-xs font-medium text-neutral-700 truncate">
                                        {column.name}
                                      </span>
                                      {column.description && (
                                        <span className="text-xs text-neutral-500 truncate">
                                          ({column.description})
                                        </span>
                                      )}
                                    </div>
                                    <span className="text-xs text-neutral-800 font-mono text-right">
                                      {displayValue.length > 20 ? `${displayValue.substring(0, 20)}...` : displayValue}
                                    </span>
                                  </div>
                                );
                              })}
                              {result.columns.length > 4 && (
                                <div className="text-xs text-neutral-500 text-center pt-2 border-t border-neutral-200/50">
                                  +{result.columns.length - 4}개 속성 더
                                </div>
                              )}
                            </div>
                          </CardContent>
                        </Card>
                      ))}
                      {result.data.length > 3 && (
                        <div className="text-center text-xs text-neutral-500 py-2">
                          ... 및 {result.data.length - 3}개 행 더
                        </div>
                      )}
                    </div>
                  </div>

                  {/* 데스크톱용 테이블 - 개선된 반응형 구조 */}
                  <div className="hidden md:block">
                    <div className="overflow-x-auto max-h-80 overflow-y-auto">
                      <table className="w-full text-xs">
                        <thead className="bg-neutral-100/60 sticky top-0">
                          <tr>
                            {result.columns.slice(0, 4).map((column, index) => (
                              <th key={index} className="px-3 py-2 text-left font-medium text-neutral-700 border-r border-neutral-200/50 last:border-r-0 min-w-[120px]">
                                <div className="flex flex-col gap-1">
                                  <div className="flex items-center gap-1">
                                    {getAttributeIcon(column.type)}
                                    <span className="truncate font-medium">{column.name}</span>
                                  </div>
                                  {column.description && column.description !== column.name && (
                                    <span className="text-xs text-neutral-500 font-normal truncate">
                                      {column.description}
                                    </span>
                                  )}
                                </div>
                              </th>
                            ))}
                            {result.columns.length > 4 && (
                              <th className="px-3 py-2 text-left font-medium text-neutral-500 min-w-[100px]">
                                <div className="flex items-center gap-1">
                                  <Info className="h-3 w-3" />
                                  <span>+{result.columns.length - 4}개 더</span>
                                </div>
                              </th>
                            )}
                          </tr>
                        </thead>
                        <tbody>
                          {result.data.slice(0, 5).map((row, rowIndex) => (
                            <tr key={rowIndex} className="border-t border-neutral-200/50 hover:bg-neutral-50/60">
                              {result.columns.slice(0, 4).map((column, colIndex) => {
                                const cellValue = row[column.name];
                                const displayValue = cellValue !== null && cellValue !== undefined ? String(cellValue) : '-';
                                const isLongValue = displayValue.length > 15;

                                return (
                                  <td key={colIndex} className="px-3 py-2 border-r border-neutral-200/50 last:border-r-0">
                                    {isLongValue ? (
                                      <HoverCard>
                                        <HoverCardTrigger asChild>
                                          <Button variant="ghost" className="h-auto p-0 text-left justify-start font-normal hover:bg-transparent">
                                            <span className="text-neutral-800 truncate block max-w-[120px] text-xs">
                                              {displayValue}
                                            </span>
                                          </Button>
                                        </HoverCardTrigger>
                                        <HoverCardContent className="w-auto max-w-sm" side="top">
                                          <div className="space-y-2">
                                            <div className="flex items-center gap-2">
                                              {getAttributeIcon(column.type)}
                                              <span className="font-medium text-sm">{column.name}</span>
                                            </div>
                                            <div className="text-sm text-neutral-700 break-all">
                                              {displayValue}
                                            </div>
                                            {column.description && (
                                              <div className="text-xs text-neutral-500 pt-1 border-t border-neutral-200">
                                                {column.description}
                                              </div>
                                            )}
                                          </div>
                                        </HoverCardContent>
                                      </HoverCard>
                                    ) : (
                                      <span className="text-neutral-800 text-xs">
                                        {displayValue}
                                      </span>
                                    )}
                                  </td>
                                );
                              })}
                              {result.columns.length > 4 && (
                                <td className="px-3 py-2 text-neutral-500">
                                  <HoverCard>
                                    <HoverCardTrigger asChild>
                                      <Button variant="ghost" size="sm" className="h-4 w-8 p-0 hover:bg-neutral-100/80">
                                        <span className="text-xs">...</span>
                                      </Button>
                                    </HoverCardTrigger>
                                    <HoverCardContent className="w-80" side="top">
                                      <div className="space-y-2">
                                        <h4 className="text-sm font-semibold">추가 속성 ({result.columns.length - 4}개)</h4>
                                        <div className="space-y-1 max-h-40 overflow-y-auto">
                                          {result.columns.slice(4).map((hiddenColumn, hiddenIndex) => {
                                            const hiddenValue = row[hiddenColumn.name];
                                            const hiddenDisplayValue = hiddenValue !== null && hiddenValue !== undefined ? String(hiddenValue) : '-';
                                            return (
                                              <div key={hiddenIndex} className="flex justify-between text-xs">
                                                <span className="text-neutral-600 flex items-center gap-1">
                                                  {getAttributeIcon(hiddenColumn.type)}
                                                  {hiddenColumn.name}
                                                  {hiddenColumn.description && (
                                                    <span className="text-neutral-500">({hiddenColumn.description})</span>
                                                  )}
                                                </span>
                                                <span className="text-neutral-800 font-mono truncate max-w-[120px]">
                                                  {hiddenDisplayValue}
                                                </span>
                                              </div>
                                            );
                                          })}
                                        </div>
                                      </div>
                                    </HoverCardContent>
                                  </HoverCard>
                                </td>
                              )}
                            </tr>
                          ))}
                          {result.data.length > 5 && (
                            <tr className="border-t border-neutral-200/50">
                              <td colSpan={Math.min(result.columns.length, 4) + (result.columns.length > 4 ? 1 : 0)} className="px-3 py-2 text-center text-neutral-500">
                                ... 및 {result.data.length - 5}개 행 더
                              </td>
                            </tr>
                          )}
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>
              </PopoverContent>
            </Popover>
          )}
        </div>
      </div>

        {/* 속성 목록 */}
        <div className="space-y-2">
          {result.columns.map((column, index) => (
            <div key={index} className="flex items-center justify-between p-2 bg-white/60 rounded border border-neutral-200/40">
              <div className="flex items-center gap-2">
                <div className={cn("w-2 h-2 rounded-full", getAttributeTypeColor(column.type).split(' ')[0])}>
                </div>
                <span className="text-sm font-medium text-neutral-900">{column.name}</span>
                <Badge variant="outline" className={cn("text-xs", getAttributeTypeColor(column.type))}>
                  {column.type}
                </Badge>
              </div>
              {column.description && (
                <span className="text-xs text-neutral-600 truncate max-w-[200px]">{column.description}</span>
              )}
            </div>
          ))}
        </div>
      
    </CompactResultTrigger>
  );
}
