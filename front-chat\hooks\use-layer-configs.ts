import { useLayerManager as useLayerManagerProvider } from "@/providers/tool-invocation-provider";
import type { GeoJSONLayerProps, LayerProps } from "@geon-map/odf";
import type { ManagedLayerProps } from "@/types/layer-manager";

/**
 * @deprecated Use useLayerManager instead for centralized layer management
 * This hook is kept for backward compatibility
 */
export const useLayerConfigs = (): LayerProps[] => {
  const { layers } = useLayerManagerProvider();

  // ManagedLayerProps를 LayerProps로 안전하게 변환
  return layers.map((layer: ManagedLayerProps): LayerProps => {
    const { source, toolCallId, ...baseProps } = layer;

    // 레이어 타입에 따른 적절한 변환
    if (layer.type === "geojson") {
      return {
        id: baseProps.id,
        type: baseProps.type,
        data: baseProps.data, // GeoJSON FeatureCollection 직접 전달
        visible: baseProps.visible ?? true, // 명시적으로 visible 속성 전달
        service: baseProps.service,
        opacity: baseProps.opacity,
        zIndex: baseProps.zIndex,
        autoFit: true,
        renderOptions: baseProps.style ? {
          style: baseProps.style
        } : undefined,
        // service: baseProps.service || "geojson",
        dataProjectionCode: baseProps.dataProjectionCode || "EPSG:5186",
        featureProjectionCode: baseProps.featureProjectionCode || "EPSG:5186",
      } as GeoJSONLayerProps;
    }

    // geoserver 레이어의 경우
    if (layer.type === "geoserver") {
      return {
        id: baseProps.id,
        type: "geoserver",
        server: baseProps.server || "",
        layer: baseProps.layer || "",
        service: baseProps.service || "wfs",
        info: baseProps.info || {
          lyrId: baseProps.id,
          lyrNm: baseProps.name || "Unknown Layer",
        },
        name: baseProps.name,
        visible: baseProps.visible ?? true, // 명시적으로 visible 속성 전달
        opacity: baseProps.opacity,
        zIndex: baseProps.zIndex,
        filter: baseProps.filter,
        bbox: baseProps.bbox,
        method: baseProps.method,
        crtfckey: baseProps.crtfckey,
        projection: baseProps.projection,
        geometryType: baseProps.geometryType,
        serviceTy: baseProps.serviceTy,
        renderOptions: baseProps.style ? {
          style: baseProps.style
        } : baseProps.renderOptions,
      } as LayerProps;
    }

    // 기타 레이어 타입들 (api, kml, csv 등)
    return {
      ...baseProps,
      // 필수 속성들 보장
      type: baseProps.type,
      id: baseProps.id,
      visible: baseProps.visible ?? true, // 명시적으로 visible 속성 전달
      autoFit: baseProps.autoFit,
      fitDuration: 100
    } as LayerProps;
  });
};

/**
 * 새로운 중앙집중식 레이어 관리 훅
 * 레이어 생성, 필터링, 스타일링을 모두 처리
 */
export const useLayerManager = () => {
  return useLayerManagerProvider();
};
