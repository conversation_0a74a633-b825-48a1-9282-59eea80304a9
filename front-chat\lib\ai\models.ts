// Define your models here.

export interface ModelCapabilities {
  reasoning: boolean;
  streaming: boolean;
  tools: boolean;
  vision: boolean;
}

export interface Model {
  id: string;
  label: string;
  apiIdentifier: string;
  description: string;
  provider: 'geon' | 'openai' | 'dify';
  capabilities: ModelCapabilities;
}

export const models: Array<Model> = [
  {
    id: 'Qwen3-4B',
    label: 'Qwen3 (추론)',
    apiIdentifier: 'Qwen/Qwen3-4B',
    description: 'Geon AI 에서 제공하는 Qwen3 추론 활성화 모델입니다.',
    provider: 'geon',
    capabilities: {
      reasoning: true,
      streaming: true,
      tools: true,
      vision: false,
    },
  },
  {
    id: 'gpt-4.1-nano',
    label: 'GPT 4.1 Nano',
    apiIdentifier: 'gpt-4.1-nano',
    description: 'OpenAI의 GPT 4.1 Nano 모델입니다.',
    provider: 'openai',
    capabilities: {
      reasoning: false,
      streaming: true,
      tools: true,
      vision: false,
    },
  },
  // {
  //   id: 'gpt-4o-mini',
  //   label: 'GPT 4o mini',
  //   apiIdentifier: 'gpt-4o-mini',
  //   description: 'OpenAI 경량 모델',
  //   provider: 'openai',
  //   capabilities: {
  //     reasoning: false,
  //     streaming: true,
  //     tools: true,
  //     vision: false,
  //   },
  // }
] as const;

export const DEFAULT_MODEL_NAME: string = 'Qwen3-4B';

// 모델 유틸리티 함수들
export function getModelById(modelId: string): Model | undefined {
  return models.find(model => model.id === modelId);
}

export function getModelCapabilities(modelId: string): ModelCapabilities | undefined {
  const model = getModelById(modelId);
  return model?.capabilities;
}

export function supportsReasoning(modelId: string): boolean {
  const capabilities = getModelCapabilities(modelId);
  return capabilities?.reasoning ?? false;
}

export function supportsStreaming(modelId: string): boolean {
  const capabilities = getModelCapabilities(modelId);
  return capabilities?.streaming ?? false;
}

export function supportsTools(modelId: string): boolean {
  const capabilities = getModelCapabilities(modelId);
  return capabilities?.tools ?? false;
}

export function supportsVision(modelId: string): boolean {
  const capabilities = getModelCapabilities(modelId);
  return capabilities?.vision ?? false;
}

export function getModelProvider(modelId: string): 'geon' | 'openai' | 'dify' | undefined {
  const model = getModelById(modelId);
  return model?.provider;
}

export function getReasoningDisabledMessage(modelId: string): string | undefined {
  const model = getModelById(modelId);
  if (!model || model.capabilities.reasoning) {
    return undefined;
  }

  // 모델별 맞춤 메시지
  switch (model.id) {
    case 'gpt-4.1-nano':
      return 'GPT 4.1 Nano 에서 지원되지 않습니다.';
    default:
      return '현재 선택된 모델은 추론 기능을 지원하지 않습니다';
  }
}
