self.__RSC_SERVER_MANIFEST="{\n  \"node\": {\n    \"7fa59543294863e81a8c0b654cf7a4eda4c9920de0\": {\n      \"workers\": {\n        \"app/(auth)/login/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(auth)/login/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/(auth)/actions.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(auth)/login/page\": \"action-browser\"\n      }\n    },\n    \"403a9f999dd94e45de5f4dd697921e202b88b72c5e\": {\n      \"workers\": {\n        \"app/(map)/geon-2d-map/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(map)/geon-2d-map/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/(map)/actions.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(map)/geon-2d-map/page\": \"action-browser\"\n      }\n    },\n    \"4043e8a8b414629d5db38590aead261d7aa2b78a0a\": {\n      \"workers\": {\n        \"app/(map)/geon-2d-map/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(map)/geon-2d-map/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/app/(map)/actions.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(map)/geon-2d-map/page\": \"action-browser\"\n      }\n    }\n  },\n  \"edge\": {},\n  \"encryptionKey\": \"GcbsATTd8lvck/zFnWhNeUaBXdbsC87dq0HdfZ6yHFk=\"\n}"