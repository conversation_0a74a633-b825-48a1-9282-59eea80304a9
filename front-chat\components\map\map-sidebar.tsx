"use client";

import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { Plus } from "lucide-react";
import Link from "next/link";

interface MapSidebarProps {
  className?: string;
}

export function MapSidebar({ className }: MapSidebarProps) {
  return (
    <div className={cn(
      "absolute top-4 left-4 z-[1000] flex flex-col gap-2 max-w-[280px]",
      className
    )}>
      {/* 새 대화 버튼 */}
        <Button
          asChild
          variant="ghost"
          size="sm"
          className="h-9 flex items-center justify-start w-full bg-background/80 hover:bg-background/90 backdrop-blur-md shadow-sm border transition-all duration-300"
        >
          <Link href="/geon-2d-map" className="flex items-center gap-2">
            <Plus className="h-4 w-4" />
            <span className="text-sm font-medium">새 대화</span>
          </Link>
        </Button>


    </div>
  );
}
