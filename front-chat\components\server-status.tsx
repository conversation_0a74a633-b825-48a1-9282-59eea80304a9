"use client";

import React, { startTransition } from 'react';
import { useServerHealth } from '@/lib/hooks/use-server-health';
import { Button } from '@/components/ui/button';
import {
  BetterTooltip,
  Tooltip,
  TooltipContent,
  TooltipTrigger
} from '@/components/ui/tooltip';
import {
  AlertCircle,
  CheckCircle,
  Loader2,
  RefreshCw,
  Wifi,
  WifiOff
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { toast } from 'sonner';
import { saveModelId } from '@/app/(map)/actions';

interface ServerStatusProps {
  selectedModelId: string;
  onModelSwitchSuggested?: () => void;
  className?: string;
}

export function ServerStatus({
  selectedModelId,
  onModelSwitchSuggested,
  className
}: ServerStatusProps) {
  // Qwen3 모델일 때만 서버 상태 체크
  const shouldCheck = selectedModelId === 'Qwen3-4B';
  const { isHealthy, isLoading, error, lastChecked, responseTime, refresh } =
    useServerHealth(shouldCheck);

  // Qwen3 모델이 아닌 경우 표시하지 않음
  if (!shouldCheck) {
    return null;
  }

  const handleRefresh = () => {
    refresh();
  };

  const handleModelSwitchSuggestion = () => {
    if (onModelSwitchSuggested) {
      onModelSwitchSuggested();
    } else {
      // 직접 모델을 GPT-4.1-nano로 변경
      startTransition(() => {
        saveModelId('gpt-4.1-nano');
        toast.success('모델이 GPT-4.1-nano로 변경되었습니다.');
        // 페이지 새로고침으로 변경사항 적용
        setTimeout(() => {
          window.location.reload();
        }, 1000);
      });
    }
  };

  const getStatusIcon = () => {
    if (isLoading) {
      return <Loader2 className="h-3 w-3 animate-spin" />;
    }

    if (isHealthy) {
      return <CheckCircle className="h-3 w-3 text-green-500" />;
    }

    return <AlertCircle className="h-3 w-3 text-red-500" />;
  };

  const getStatusText = () => {
    if (isLoading) return '확인 중...';
    if (isHealthy) return '정상';
    return '오프라인';
  };

  const getStatusColor = () => {
    if (isLoading) return 'text-yellow-600';
    if (isHealthy) return 'text-green-600';
    return 'text-red-600';
  };

  const getTooltipContent = () => {
    const baseInfo = `Qwen3 서버 상태: ${getStatusText()}`;

    if (isLoading) {
      return baseInfo;
    }

    if (isHealthy && responseTime) {
      return `${baseInfo}\n응답시간: ${responseTime}ms\n마지막 확인: ${lastChecked?.toLocaleTimeString()}`;
    }

    if (error) {
      return `${baseInfo}\n오류: ${error}\n마지막 확인: ${lastChecked?.toLocaleTimeString()}\n\nGPT-4.1-nano 모델 사용을 권장합니다.`;
    }

    return baseInfo;
  };

  return (
    <div className={cn("flex items-center gap-1", className)}>
      <BetterTooltip content={getTooltipContent()}>
        <Button
          variant="ghost"
          size="icon"
          className="h-6 w-6"
          onClick={handleRefresh}
          disabled={isLoading}
        >
          <div className="flex items-center gap-1 px-2 py-1 rounded-md bg-secondary/20 hover:bg-secondary/30 transition-colors">
            {getStatusIcon()}
            <span className={cn("text-xs font-medium", getStatusColor())}>
              {getStatusText()}
            </span>
          </div>
        </Button>
      </BetterTooltip>

      {/* 서버가 오프라인일 때 모델 변경 제안 버튼 */}
      {!isHealthy && !isLoading && (
        <BetterTooltip content="Qwen3 서버가 오프라인입니다. GPT-4.1-nano 모델로 자동 변경합니다.">
          <Button
            variant="destructive"
            size="sm"
            className="h-6 px-2 text-xs animate-pulse"
            onClick={handleModelSwitchSuggestion}
          >
            GPT로 변경
          </Button>
        </BetterTooltip>
      )}
    </div>
  );
}

// 간단한 상태 표시용 컴포넌트 (헤더용)
export function ServerStatusIndicator({
  selectedModelId,
  className
}: {
  selectedModelId: string;
  className?: string;
}) {
  const shouldCheck = selectedModelId === 'Qwen3';
  const { isHealthy, isLoading } = useServerHealth(shouldCheck);

  if (!shouldCheck) return null;

  return (
    <div className={cn("flex items-center", className)}>
      {isLoading ? (
        <Wifi className="h-3 w-3 text-yellow-500 animate-pulse" />
      ) : isHealthy ? (
        <Wifi className="h-3 w-3 text-green-500" />
      ) : (
        <WifiOff className="h-3 w-3 text-red-500" />
      )}
    </div>
  );
}
