"use client";

import React from "react";
import { cn } from "@/lib/utils";
import { BasemapButton } from "./basemap-button";
import { LayerListDialog } from "../chat-map/layer-list-dialog";
import { UseMapReturn } from "@geon-map/odf";

interface MapControlsProps {
  mapState?: UseMapReturn;
  className?: string;
}

export function MapControls({ mapState, className }: MapControlsProps) {
  return (
    <div className={cn(
      "absolute top-4 right-4 z-[1000] flex flex-col gap-2 max-w-[280px]",
      className
    )}>
      {/* 레이어 검색 다이얼로그 */}
      <LayerListDialog />

      {/* 배경지도 버튼 */}
      {mapState && <BasemapButton mapState={mapState} />}
    </div>
  );
}
