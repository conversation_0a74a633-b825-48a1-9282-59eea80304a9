"use client";

import type { J<PERSON>NValue } from "ai";
import { Message as AIMessage } from "ai";
import React from "react";
import { Reasoning } from "./reasoning";
import { Badge } from "./ui/badge";
import {
  Search,
  MapPin,
  Route,
  Layers3,
  Settings,
  MessageSquare,
  CheckCircle2,
  <PERSON>ap,
  Filter,
  Edit2,
  Edit,
  Eraser,
  Map
} from "lucide-react";
import { AnimatePresence, motion } from "framer-motion";
import { AnimatedShinyText } from "./magicui/animated-shiny-text";
// getAgentFriendlyMessage import 제거 - 더 이상 사용하지 않음
// Orchestrator Status Annotation Types
type OrchestratorStatusValue = "planning" | "executing_step" | "done";

interface BaseOrchestratorStatusAnnotation {
  type: "status";
  value: OrchestratorStatusValue;
  [key: string]: JSONValue;
}

interface PlanningStatusAnnotation extends BaseOrchestratorStatusAnnotation {
  value: "planning";
}

export interface ExecutingStepStatusAnnotation
  extends BaseOrchestratorStatusAnnotation {
  value: "executing_step";
  agentName: string;
  stepNumber: number;
  totalSteps: number;
  goal: string;
}

interface DoneStatusAnnotation extends BaseOrchestratorStatusAnnotation {
  value: "done";
}

export type OrchestratorStatusAnnotation =
  | PlanningStatusAnnotation
  | ExecutingStepStatusAnnotation
  | DoneStatusAnnotation;

// Application-specific Annotation Types
interface BaseAppAnnotation {
  type: string;
  message: JSONValue;
  [key: string]: JSONValue | undefined;
}

export interface AgentStartAnnotation extends BaseAppAnnotation {
  type: "agent_start";
  agent: string;
  message: string;
}

export interface IntentAnalyzedAnnotation extends BaseAppAnnotation {
  type: "intent_analyzed";
  intent: string;
  message: string;
}

export interface AgentCompletedAnnotation extends BaseAppAnnotation {
  type: "agent_completed";
  agent: string;
  message: string;
}

export interface ProgressUpdateAnnotation extends BaseAppAnnotation {
  type: "progress_update";
  message: string;
  stage: "analyzing" | "processing" | "calculating" | "completing";
}

export interface ToolCallAnnotation extends BaseAppAnnotation {
  type: "tool_call";
  toolName: string;
  args: any;
}

// 평가 관련 어노테이션 타입들
export interface EvaluationStartAnnotation extends BaseAppAnnotation {
  type: "evaluation_start";
  iteration: number;
  maxIterations: number;
  message: string;
}

export interface EvaluationCompletedAnnotation extends BaseAppAnnotation {
  type: "evaluation_completed";
  iteration: number;
  maxIterations: number;
  isCompleted: boolean;
  shouldContinue: boolean;
  message: string;
  reason: string;
  improvementSuggestions?: JSONValue;
}

export interface RetryStartingAnnotation extends BaseAppAnnotation {
  type: "retry_starting";
  iteration: number;
  maxIterations: number;
  message: string;
  reason: string;
  improvementSuggestions: JSONValue;
}

export interface RetryCompletedAnnotation extends BaseAppAnnotation {
  type: "retry_completed";
  totalIterations: number;
  maxIterations: number;
  finalResult: "success" | "partial";
  message: string;
}

export interface RetryLimitReachedAnnotation extends BaseAppAnnotation {
  type: "retry_limit_reached";
  totalIterations: number;
  maxIterations: number;
  message: string;
  finalEvaluation: any;
}

export type AppSpecificAnnotation =
  | AgentStartAnnotation
  | IntentAnalyzedAnnotation
  | AgentCompletedAnnotation
  | ProgressUpdateAnnotation
  | ToolCallAnnotation
  | EvaluationStartAnnotation
  | EvaluationCompletedAnnotation
  | RetryStartingAnnotation
  | RetryCompletedAnnotation
  | RetryLimitReachedAnnotation;

// Combined annotation type for filtering
export type RelevantAnnotation =
  | OrchestratorStatusAnnotation
  | AppSpecificAnnotation;



interface AnnotationsProps {
  annotations?: AIMessage["annotations"];
  isLoading: boolean;
}

// 도구 이름을 사용자 친화적으로 변환하고 아이콘 추가
export const getToolDisplayInfo = (toolName: string) => {
  const toolMap: Record<string, { label: string; icon: React.ReactNode; variant: "default" | "secondary" | "outline" }> = {
    // 검색/조회 도구들 (Search 아이콘)
    getLayer: { label: "레이어 조회", icon: <Search className="h-3 w-3" />, variant: "default" },
    getLayerInfo: { label: "레이어 정보", icon: <Search className="h-3 w-3" />, variant: "default" },
    getLayerAttributes: { label: "속성 조회", icon: <Search className="h-3 w-3" />, variant: "default" },
    getLayerList: { label: "레이어 검색", icon: <Search className="h-3 w-3" />, variant: "default" },
    searchAddress: { label: "주소 검색", icon: <Search className="h-3 w-3" />, variant: "default" },
    searchOrigin: { label: "출발지 검색", icon: <MapPin className="h-3 w-3 text-green-600" />, variant: "default" },
    searchDestination: { label: "목적지 검색", icon: <MapPin className="h-3 w-3 text-red-600" />, variant: "default" },
    searchDirections: { label: "경로 탐색", icon: <Route className="h-3 w-3" />, variant: "default" },



    // 지도 조작 도구들 (MapPin 아이콘)
    setCenter: { label: "지도 이동", icon: <MapPin className="h-3 w-3" />, variant: "outline" },
    changeBasemap: { label: "배경지도 변경", icon: <Map className="h-3 w-3" />, variant: "outline" },
    highlightGeometry: { label: "영역 강조", icon: <MapPin className="h-3 w-3" />, variant: "outline" },
    moveMapByDirection: { label: "방향 이동", icon: <MapPin className="h-3 w-3" />, variant: "outline" },
    setMapZoom: { label: "확대/축소", icon: <MapPin className="h-3 w-3" />, variant: "outline" },

    // 레이어 관련 도구들 (Layers3 아이콘)
    createLayerFilter: { label: "필터 적용", icon: <Filter className="h-3 w-3" />, variant: "secondary" },
    createVectorStyle: { label: "스타일 생성", icon: <Layers3 className="h-3 w-3" />, variant: "secondary" },
    updateLayerStyle: { label: "스타일 변경", icon: <Edit2 className="h-3 w-3" />, variant: "secondary" },
    removeLayer: { label: "레이어 삭제", icon: <Eraser className="h-3 w-3" />, variant: "secondary" },
    generateCategoricalStyle: { label: "유형별 스타일", icon: <Edit className="h-3 w-3" />, variant: "secondary" },

    // 분석 도구들 (Zap 아이콘)
    performDensityAnalysis: { label: "밀도 분석", icon: <Zap className="h-3 w-3" />, variant: "outline" },

    // HIL 도구들 (MessageSquare 아이콘)
    chooseOption: { label: "옵션 선택 제안", icon: <MessageSquare className="h-3 w-3" />, variant: "outline" },
    getUserInput: { label: "입력 요청 제안", icon: <MessageSquare className="h-3 w-3" />, variant: "outline" },
    confirmWithCheckbox: { label: "확인", icon: <CheckCircle2 className="h-3 w-3" />, variant: "outline" },
    getLocation: { label: "위치 정보 요청", icon: <MapPin className="h-3 w-3" />, variant: "outline" },
  };

  return toolMap[toolName] || {
    label: toolName,
    icon: <Settings className="h-3 w-3" />,
    variant: "secondary" as const
  };
};

export const Annotations = ({ annotations, isLoading }: AnnotationsProps) => {
  if (!annotations || annotations.length === 0) return null;

  // 상태 변수들
  const reasoningData: string[] = [];
  let isIntentAnalyzed = false;
  let intentMessage = "";
  let isAgentStarted = false;
  let isAgentCompleted = false;
  const toolCalls: string[] = [];

  // 평가 관련 상태 변수들
  let evaluationResult: EvaluationCompletedAnnotation | null = null;
  let retryInfo: RetryStartingAnnotation | null = null;
  let finalResult: RetryCompletedAnnotation | RetryLimitReachedAnnotation | null = null;

  // agent_completed 이후의 새로운 대화 시작을 감지
  let lastAgentCompletedIndex = -1;
  let hasNewConversation = false;

  // 먼저 agent_completed의 위치들을 찾고, 그 이후에 reasoning이 있는지 확인
  annotations.forEach((ann: JSONValue, index) => {
    if (typeof ann === "object" && ann !== null && !Array.isArray(ann)) {
      if ("type" in ann && ann.type === "agent_completed") {
        // agent_completed 이후에 reasoning이 있는지 확인
        const hasReasoningAfter = annotations.slice(index + 1).some((nextAnn: JSONValue) => {
          return typeof nextAnn === "object" && nextAnn !== null && !Array.isArray(nextAnn) &&
            "type" in nextAnn && nextAnn.type === "reasoning";
        });

        if (hasReasoningAfter) {
          lastAgentCompletedIndex = index;
          hasNewConversation = true;
        }
      }
    }
  });

  // 먼저 전체 어노테이션에서 toolCalls 수집 (새 대화 여부와 관계없이)
  // 중복 제거하지 않고 호출된 만큼 모두 수집
  annotations.forEach((ann: JSONValue) => {
    if (typeof ann === "object" && ann !== null && !Array.isArray(ann)) {
      if ("type" in ann && ann.type === "tool_call") {
        const toolAnn = ann as ToolCallAnnotation;
        toolCalls.push(toolAnn.toolName);
      }
    }
  });

  // 어노테이션을 분석하여 상태 파악
  annotations.forEach((ann: JSONValue, index) => {
    if (typeof ann === "object" && ann !== null && !Array.isArray(ann)) {
      // messageIdFromServer는 건너뛰기
      if ("messageIdFromServer" in ann) {
        return;
      }

      // 새로운 대화가 시작된 경우, 마지막 agent_completed 이전의 상태는 무시 (reasoning 제외)
      if (hasNewConversation && lastAgentCompletedIndex >= 0 && index <= lastAgentCompletedIndex) {
        // reasoning만 누적하고 나머지는 무시
        if ("type" in ann && ann.type === "reasoning" && "textDelta" in ann) {
          reasoningData.push(ann.textDelta as string);
        }
        return;
      }

      if ("type" in ann && ann.type === "reasoning" && "textDelta" in ann) {
        reasoningData.push(ann.textDelta as string);
      } else if ("type" in ann && ann.type === "intent_analyzed") {
        isIntentAnalyzed = true;
        const intentAnn = ann as IntentAnalyzedAnnotation;
        intentMessage = intentAnn.message;
      } else if ("type" in ann && ann.type === "agent_start") {
        isAgentStarted = true;
      } else if ("type" in ann && ann.type === "agent_completed") {
        // 새로운 대화가 아닌 경우에만 완료 상태로 설정
        if (!hasNewConversation || index > lastAgentCompletedIndex) {
          isAgentCompleted = true;
        }
      } else if ("type" in ann && ann.type === "evaluation_completed") {
        evaluationResult = ann as EvaluationCompletedAnnotation;
      } else if ("type" in ann && ann.type === "retry_starting") {
        retryInfo = ann as RetryStartingAnnotation;
      } else if ("type" in ann && (ann.type === "retry_completed" || ann.type === "retry_limit_reached")) {
        finalResult = ann as RetryCompletedAnnotation | RetryLimitReachedAnnotation;
      }
    }
  });

  // 어노테이션을 표시할지 결정 (사용하지 않으므로 제거)

  return (
    <div className="w-full max-w-4xl mx-auto">

      {/* 1. 의도분석 진행중 상태 */}
      {!isIntentAnalyzed && isLoading && reasoningData.length > 0 && (
        <div className="mb-4 px-1 relative">
          <div className="absolute left-0 top-0 bottom-0 w-1 bg-blue-500 rounded-full"></div>
          <div className="pl-4">
            <div className="flex items-center gap-2">
              <AnimatedShinyText className="text-sm inline-flex items-center justify-center px-2 py-1 transition ease-out hover:text-neutral-600 hover:duration-300 hover:dark:text-neutral-400">
                메세지를 이해하고 있어요...
              </AnimatedShinyText>
            </div>
          </div>
        </div>
      )}

      {/* reasoning 컴포넌트 (항상 표시) */}
      {reasoningData.length > 0 && (
        <div className="mb-4">
          <Reasoning
            reasoning={reasoningData.join("")}
            className="my-2"
            initialOpen={false}
            // initialOpen={!isIntentAnalyzed}
            isReasoning={isLoading && !isIntentAnalyzed}
          />
        </div>
      )}


      {/* 2. 의도분석 완료 - 작업 계획 메시지 */}
      {isIntentAnalyzed && intentMessage && (
        <div className="mb-4 px-1 relative">
          <div className="absolute left-0 top-0 bottom-0 w-1 bg-gradient-to-b from-blue-500 to-purple-500 rounded-full"></div>
          <div className="pl-4">
            <p className="text-sm text-gray-700 leading-relaxed font-medium">
              {intentMessage}
            </p>
          </div>
        </div>
      )}

      {/* 3. 에이전트 실행 - 도구 호출 표시 */}
      {isAgentStarted && (
        <div className="mb-4 px-1 relative">
          <div className="absolute left-0 top-0 bottom-0 w-1 bg-gray-200 rounded-full"></div>
          <div className="pl-4">
            <div className="flex items-center gap-2 mb-2">
              {!isAgentCompleted &&
                <span className="text-sm font-medium text-gray-800">작업 중</span>
              }
              {!isAgentCompleted && (
                <div className="w-2 h-2 bg-orange-500 rounded-full animate-pulse"></div>
              )}
            </div>

            {/* 도구 호출 뱃지들 - 동적으로 표시 */}
            {toolCalls.length > 0 && (
              <div className="flex flex-wrap gap-2">
                {toolCalls.map((toolName, index) => {
                  const toolInfo = getToolDisplayInfo(toolName);

                  return (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, scale: 0.5 }}
                      animate={{ opacity: 1, scale: 1 }}
                      exit={{ opacity: 0, scale: 0.5 }}
                      transition={{ duration: 0.2 }}
                    >
                      <Badge
                        key={index}
                        variant={toolInfo.variant}
                        className="text-xs px-2 py-1 font-medium flex items-center gap-1"
                      >
                        {toolInfo.icon}
                        {toolInfo.label}
                      </Badge>
                    </motion.div>
                  );
                })}
              </div>
            )}
          </div>
        </div>
      )}

      {/* 4. 평가 완료 결과 */}
      {evaluationResult && (
        <div className="mb-4 px-1 relative">
          <div className={`absolute left-0 top-0 bottom-0 w-1 rounded-full ${
            (evaluationResult as EvaluationCompletedAnnotation).isCompleted ? 'bg-green-500' : 'bg-yellow-500'
          }`}></div>
          <div className="pl-4">
            <div className="flex items-center gap-2 mb-2">
              {(evaluationResult as EvaluationCompletedAnnotation).isCompleted ? (
                <CheckCircle2 className="h-4 w-4 text-green-600" />
              ) : (
                <Zap className="h-4 w-4 text-yellow-600" />
              )}
              <span className="text-sm font-medium">
                {(evaluationResult as EvaluationCompletedAnnotation).message}
              </span>
            </div>

            {/* 평가 이유 표시 */}
            <div className="text-xs text-gray-600 mt-1">
              <div className="font-medium mb-1">평가 내용</div>
              <p>{(evaluationResult as EvaluationCompletedAnnotation).reason}</p>
            </div>

            {/* 개선 제안 표시 */}
            {(() => {
              const suggestions = (evaluationResult as EvaluationCompletedAnnotation).improvementSuggestions;
              if (Array.isArray(suggestions) && suggestions.length > 0) {
                return (
                  <div className="text-xs text-gray-600 mt-2">
                    <div className="font-medium mb-1">개선 제안</div>
                    <ul className="list-disc list-inside space-y-1">
                      {suggestions.map((suggestion: any, index: number) => (
                        <li key={index}>{String(suggestion)}</li>
                      ))}
                    </ul>
                  </div>
                );
              }
              return null;
            })()}
          </div>
        </div>
      )}

      {/* 6. 재시도 시작 */}
      {retryInfo && (
        <div className="mb-4 px-1 relative">
          <div className="absolute left-0 top-0 bottom-0 w-1 bg-blue-500 rounded-full"></div>
          <div className="pl-4">
            <div className="flex items-center gap-2 mb-2">
              <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
              <span className="text-sm font-medium text-blue-700">
                {(retryInfo as RetryStartingAnnotation).message}
              </span>
            </div>
            <div className="text-xs text-gray-600">
              {(retryInfo as RetryStartingAnnotation).reason}
            </div>
          </div>
        </div>
      )}

      {/* 7. 최종 결과 */}
      {finalResult && (
        <div className="mb-4 px-1 relative">
          <div className={`absolute left-0 top-0 bottom-0 w-1 rounded-full ${
            (finalResult as any).type === "retry_completed" && (finalResult as RetryCompletedAnnotation).finalResult === "success"
              ? 'bg-green-500' : 'bg-orange-500'
          }`}></div>
          <div className="pl-4">
            <div className="flex items-center gap-2">
              <span className="text-sm font-medium">
                {(finalResult as any).message}
              </span>
            </div>
            <div className="text-xs text-gray-600 mt-1">
              총 {(finalResult as any).totalIterations}회 시도 완료
            </div>
          </div>
        </div>
      )}

      {/* 8. 완료 상태 */}
      {/* {isAgentCompleted && !isLoading && (
        <div className="mt-3 px-1 relative">
          <div className="absolute left-0 top-0 bottom-0 w-1 bg-green-500 rounded-full"></div>
          <div className="pl-4">
            <p className="text-sm text-green-700 font-medium">
              완료되었습니다
            </p>
          </div>
        </div>
      )} */}

      {/* 스트리밍 완료 후 최소한의 상태 표시 */}
      {!isLoading && !isIntentAnalyzed && !isAgentStarted && !isAgentCompleted && reasoningData.length > 0 && (
        <div className="mt-3 px-1 relative">
          <div className="absolute left-0 top-0 bottom-0 w-1 bg-gray-300 rounded-full"></div>
          <div className="pl-4">
            <p className="text-sm text-gray-600">
              처리 완료
            </p>
          </div>
        </div>
      )}

    </div>
  );
};
