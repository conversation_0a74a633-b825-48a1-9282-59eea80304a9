{"node": {"7fa59543294863e81a8c0b654cf7a4eda4c9920de0": {"workers": {"app/(auth)/login/page": {"moduleId": "[project]/.next-internal/server/app/(auth)/login/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/(auth)/actions.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/(auth)/login/page": "action-browser"}}, "403a9f999dd94e45de5f4dd697921e202b88b72c5e": {"workers": {"app/(map)/geon-2d-map/page": {"moduleId": "[project]/.next-internal/server/app/(map)/geon-2d-map/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/(map)/actions.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/(map)/geon-2d-map/page": "action-browser"}}, "4043e8a8b414629d5db38590aead261d7aa2b78a0a": {"workers": {"app/(map)/geon-2d-map/page": {"moduleId": "[project]/.next-internal/server/app/(map)/geon-2d-map/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/(map)/actions.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/(map)/geon-2d-map/page": "action-browser"}}}, "edge": {}, "encryptionKey": "GcbsATTd8lvck/zFnWhNeUaBXdbsC87dq0HdfZ6yHFk="}