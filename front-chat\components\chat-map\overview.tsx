import { motion } from 'framer-motion';
import { BotIcon, EarthIcon, MapIcon, LayersIcon, NavigationIcon, ImageIcon, ChevronRightIcon, SparklesIcon, SearchIcon, FilterIcon, SettingsIcon } from 'lucide-react';
import { Card, CardContent } from "@/components/ui/card";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Button } from "@/components/ui/button";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";

interface ExampleCategory {
  title: string;
  icon: any;
  examples: Array<{
    label: string;
    command: string;
  }>;
}

interface OverviewProps {
  setInput: (input: string) => void;
}

export const Overview = ({ setInput }: OverviewProps) => {
  const examples: ExampleCategory[] = [
    {
      title: "장소 검색 및 이동",
      icon: <MapIcon className="w-4 h-4 text-primary" />,
      examples: [
        { label: "위치 이동", command: "웨이버스로 이동해줘" },
        { label: "길찾기", command: "웨이버스에서 평촌역까지 얼마나 걸려?" },
        { label: "현재위치에서 길찾기", command: "내 위치에서 구로디지털단지역까지 얼마나걸려?" },
        // { label: "내 위치", command: "내 위치로 이동해줘" },
      ]
    },
    {
      title: "지도 제어",
      icon: <SettingsIcon className="w-4 h-4 text-primary" />,
      examples: [
        { label: "지도 확대", command: "지도를 확대해줘" },
        { label: "지도 축소", command: "지도를 축소해줘" },
        { label: "위쪽으로 500m 이동", command: "위쪽으로 500m 이동해줘" },
        { label: "항공지도로 변경", command: "배경지도를 항공지도로 변경해줘" }
      ]
    },
    {
      title: "레이어 제어",
      icon: <LayersIcon className="w-4 h-4 text-primary" />,
      examples: [
        { label: "레이어 추가", command: "택지개발사업 레이어를 추가해줘" },
        { label: "단일 스타일 설정", command: "전국에 있는 백년가게를 노란색 별모양으로 보여줄래?" },
        { label: "유형별 스타일 설정", command: "서울에 있는 약국만 빨간색으로 표시해줘" },
        { label: "유형별 스타일 설정", command: "서울의 건물을 5층까지 노란색, 10층까지 파란색, 나머지는 빨간색으로 표시해줄래?" },
      ]
    },
    {
      title: "데이터 분석",
      icon: <FilterIcon className="w-4 h-4 text-primary" />,
      examples: [
        { label: "노후화 건물 분석", command: "서울의 노후화된 건물을 보여줘" },
        { label: "레이어 밀도 분석", command: "AI 발생농가 지역의 밀집도를 분석해줘" },
      ]
    },
  ];

  return (
    <motion.div
      key="overview"
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -10 }}
      transition={{ duration: 0.3 }}
      className="w-full max-w-lg backdrop-blur-sm mx-auto bg-gradient-to-b from-background/10 to-background/80 rounded-xl"
    >
      <Card className="border-none shadow-none bg-transparent">
        <CardContent className="p-6 space-y-6">
          {/* Header Section */}
          <div className="relative">
            <motion.div
              className="flex items-center justify-center gap-4"
              initial={{ scale: 0.9 }}
              animate={{ scale: 1 }}
              transition={{ delay: 0.2, type: "spring", stiffness: 150 }}
            >
              <div className="relative">
                <EarthIcon size={32} className="text-emerald-600" />
                <motion.div
                  className="absolute -top-1 -right-1"
                  animate={{ rotate: 360 }}
                  transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
                >
                  <SparklesIcon size={16} className="text-yellow-500" />
                </motion.div>
              </div>
              <span className="font-bold text-3xl bg-gradient-to-r from-emerald-600 to-blue-600 bg-clip-text text-transparent">×</span>
              <BotIcon size={32} className="text-blue-600" />
            </motion.div>
            <motion.div
              className="text-center mt-4 space-y-2"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.3 }}
            >
              <h2 className="text-xl font-bold bg-gradient-to-r from-emerald-600 via-blue-600 to-purple-600 bg-clip-text text-transparent">
                말로 만드는 지도
              </h2>
              <p className="text-sm text-muted-foreground">
                자연어로 지도를 제어해보세요.
              </p>
            </motion.div>
          </div>

          {/* Main Content */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.4 }}
            className="space-y-4"
          >


            <Accordion type="single" collapsible defaultValue="examples" className="w-full bg-background/40 rounded-lg border border-border/20">
              <AccordionItem value="examples" className="border-none">
                <AccordionTrigger className="justify-center gap-2 py-3 hover:no-underline">
                  <span className="text-sm font-medium">✨ 기능 둘러보기</span>
                </AccordionTrigger>
                <AccordionContent className="flex py-4 justify-center pr-5">
                  <motion.div
                    className="space-y-4"
                    variants={{
                      hidden: { opacity: 0 },
                      show: {
                        opacity: 1,
                        transition: {
                          staggerChildren: 0.1
                        }
                      }
                    }}
                    initial="hidden"
                    animate="show"
                  >
                    {examples.map((category, idx) => (
                      <motion.div
                        key={idx}
                        variants={{
                          hidden: { opacity: 0, x: -10 },
                          show: { opacity: 1, x: 0 }
                        }}
                        className="space-y-2"
                      >
                        <Popover>
                          <PopoverTrigger asChild>
                            <Button
                              variant="ghost"
                              className="w-full justify-start gap-2 hover:bg-primary/10"
                            >
                              {category.icon}
                              <span>{category.title}</span>
                            </Button>
                          </PopoverTrigger>
                          <PopoverContent className="w-80 p-4 bg-gradient-to-b from-background/95 to-background/98 backdrop-blur-lg border border-border/40 shadow-lg animate-in fade-in-0 zoom-in-95 duration-200">
                            <div className="space-y-4">
                              {/* 헤더 섹션 */}
                              <div className="border-b border-border/40 pb-3">
                                <div className="flex items-center gap-2 mb-2">
                                  {category.icon}
                                  <h4 className="font-semibold text-sm bg-gradient-to-r from-primary to-primary/80 bg-clip-text text-transparent">
                                    {category.title}
                                  </h4>
                                </div>
                                <p className="text-xs text-muted-foreground/80">아래 예시 중에서 선택해보세요</p>
                              </div>

                              {/* 예시 목록 */}
                              <div className="space-y-2">
                                {category.examples.map((example, index) => (
                                  <Button
                                    key={index}
                                    variant="ghost"
                                    className="w-full group justify-between relative overflow-hidden rounded-lg px-0"
                                    onClick={() => setInput(example.command)}
                                  >
                                    <div className="absolute inset-0 bg-gradient-to-r from-primary/5 to-primary/10 opacity-0 group-hover:opacity-100 transition-opacity duration-200" />
                                    <div className="relative flex flex-col items-start gap-1 p-3 ">
                                      <span className="font-medium text-sm text-foreground/90 group-hover:text-primary transition-colors">
                                        {example.label}
                                      </span>
                                      <span className="text-xs text-muted-foreground/70 group-hover:text-muted-foreground transition-colors">
                                        {example.command}
                                      </span>
                                    </div>
                                    <div className="absolute right-3 top-1/2 -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                                      <ChevronRightIcon className="w-4 h-4 text-primary" />
                                    </div>
                                  </Button>
                                ))}
                              </div>
                            </div>
                          </PopoverContent>
                        </Popover>
                      </motion.div>
                    ))}
                  </motion.div>
                </AccordionContent>
              </AccordionItem>
            </Accordion>
          </motion.div>

          {/* Footer */}
          <motion.p
            className="text-xs text-center text-muted-foreground"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.5 }}
          >
            각 예시를 클릭하면 자동으로 입력됩니다
          </motion.p>
        </CardContent>
      </Card>
    </motion.div>
  );
};

export default Overview;