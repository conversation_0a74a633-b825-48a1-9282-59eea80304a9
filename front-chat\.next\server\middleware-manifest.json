{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_c50ec21a._.js", "server/edge/chunks/9c5b9_@auth_core_16e273d6._.js", "server/edge/chunks/96a70_jose_dist_webapi_68ebb33a._.js", "server/edge/chunks/node_modules__pnpm_60fd9d8f._.js", "server/edge/chunks/[root-of-the-server]__b9a0598e._.js", "server/edge/chunks/edge-wrapper_22e6e254.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api\\/|_next\\/|_static\\/|_vercel|[\\w-]+\\.\\w+).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api/|_next/|_static/|_vercel|[\\w-]+\\.\\w+).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "GcbsATTd8lvck/zFnWhNeUaBXdbsC87dq0HdfZ6yHFk=", "__NEXT_PREVIEW_MODE_ID": "140315fdf92025f3072da3d2079001c8", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "901d3904cf33e2043dd79c0eae0ecf22c65a0b635629fa0e3642a48dd18542f9", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "041e8ce5be42e3422112040974a95194df919098d94349b26544719969507752"}}}, "sortedMiddleware": ["/"], "functions": {}}