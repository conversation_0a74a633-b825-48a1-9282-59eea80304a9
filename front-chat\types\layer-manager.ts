import { UseMapReturn } from "@geon-map/odf";
import { LayerStyle } from "./layer-style";
import { DirectionsResponse } from "@/types/tools";

// GeoJSON 타입 정의 (ODF 호환)
interface GeoJSONCoordinate extends Array<number> {
  0: number; // longitude/x
  1: number; // latitude/y
}

interface GeoJSONLineString {
  type: "LineString";
  coordinates: GeoJSONCoordinate[];
}

interface GeoJSONFeature {
  type: "Feature";
  geometry: GeoJSONLineString;
  properties: {
    name?: string;
    distance?: number;
    duration?: number;
    toolCallId?: string;
    [key: string]: any;
  };
}

interface GeoJSONFeatureCollection {
  type: "FeatureCollection";
  features: GeoJSONFeature[];
}

// 레이어 상태 관리를 위한 확장 타입
export interface ManagedLayerProps {
  // 기본 식별자
  id: string;
  name?: string;
  visible?: boolean;
  opacity?: number;
  zIndex?: number;
  filter?: string;
  style?: LayerStyle | any;
  source?: 'tool-result' | 'user-added';
  toolCallId?: string; // tool 결과와 연결
  userModified?: boolean; // 사용자가 수동으로 수정했는지 여부

  // ODF LayerProps와 호환되는 필수 속성들
  type: string;

  // Geoserver 레이어 속성들 (선택적)
  server?: string;
  layer?: string;
  service?: string;
  bbox?: boolean;
  method?: 'get' | 'post';
  crtfckey?: string;
  projection?: string;
  geometryType?: string;
  serviceTy?: string;

  // GeoJSON 레이어 속성들 (선택적)
  data?: any;
  dataProjectionCode?: string;
  featureProjectionCode?: string;

  // 레이어 정보
  info?: {
    lyrId: string;
    lyrNm: string;
    description?: string;
    metadata?: {
      cntntsId: string;
      jobClCode?: string;
      lyrClCode: string;
      lyrTySeCode: string;
      namespace?: string;
    };
  };

  // 기타 속성들
  [key: string]: any;
}

// 레이어 액션 타입
export type LayerAction =
  | { type: 'ADD_LAYER'; payload: ManagedLayerProps }
  | { type: 'UPDATE_LAYER'; payload: { id: string; updates: Partial<ManagedLayerProps> } }
  | { type: 'REMOVE_LAYER'; payload: string }
  | { type: 'TOGGLE_VISIBILITY'; payload: string }
  | { type: 'UPDATE_FILTER'; payload: { id: string; filter: string } }
  | { type: 'UPDATE_STYLE'; payload: { id: string; style: LayerStyle | any } }
  | { type: 'UPDATE_Z_INDEX'; payload: { id: string; zIndex: number } }
  | { type: 'REORDER_LAYERS'; payload: { layerIds: string[] } }
  | { type: 'SET_LAYERS'; payload: ManagedLayerProps[] };

// Tool 결과에서 레이어로 변환하는 함수 타입
export type LayerTransformer<T = any> = (toolResult: T, toolCallId: string, mapState?: UseMapReturn) => ManagedLayerProps;

// 레이어 관리자 컨텍스트 타입
export interface LayerManagerContext {
  layers: ManagedLayerProps[];
  addLayer: (layer: ManagedLayerProps) => void;
  updateLayer: (id: string, updates: Partial<ManagedLayerProps>) => void;
  removeLayer: (id: string) => void;
  toggleVisibility: (id: string) => void;
  updateFilter: (id: string, filter: string) => void;
  updateStyle: (id: string, style: LayerStyle | any) => void;
  updateZIndex: (id: string, zIndex: number) => void;
  reorderLayers: (layerIds: string[]) => void;
  getLayerById: (id: string) => ManagedLayerProps | undefined;
}

// Tool 결과 변환기들
export const layerTransformers: Record<string, LayerTransformer> = {
  getLayer: (result: any, toolCallId: string, mapState?: UseMapReturn): ManagedLayerProps => ({
    id: result.lyrId || result.id || toolCallId,
    name: result.lyrNm || result.name,
    type: result.type || 'geoserver',
    service: result.service,
    server: result.server,
    layer: result.layer,
    crtfckey: result.crtfckey,
    bbox: result.bbox,
    method: result.method,
    projection: result.projection,
    geometryType: result.geometryType,
    serviceTy: result.serviceTy,
    visible: result.visible !== false, // 기본값 true
    opacity: result.opacity ?? 1,
    zIndex: result.zIndex ?? 0,
    filter: result.filter,
    style: result.style,
    source: 'tool-result',
    toolCallId,
    info: result.info || {
      lyrId: result.lyrId || result.id || toolCallId,
      lyrNm: result.lyrNm || result.name || 'Unknown Layer',
      description: result.description,
      metadata: result.metadata
    }
  }),

  performDensityAnalysis: (result: any, toolCallId: string): ManagedLayerProps => ({
    id: `density-${toolCallId}`,
    name: `밀도 분석 - ${result.layerName || 'Unknown'}`,
    type: "geojson",
    data: { ...result },
    service: "heatmap",
    dataProjectionCode: "EPSG:5186",
    featureProjectionCode: "EPSG:5179",
    visible: true,
    opacity: 0.8,
    zIndex: 100,
    source: 'tool-result',
    toolCallId,
    info: {
      lyrId: `density-${toolCallId}`,
      lyrNm: `밀도 분석 - ${result.layerName || 'Unknown'}`,
      description: '밀도 분석 결과 레이어'
    }
  }),

  searchDirections: (result: DirectionsResponse, toolCallId: string, mapState?: UseMapReturn): ManagedLayerProps => {
    const route = result.routes?.[0];

    // 거리와 시간 정보 포맷팅
    const distanceText = route?.summary?.distance
      ? `${(route.summary.distance / 1000).toFixed(1)}km`
      : '거리 정보 없음';
    const durationText = route?.summary?.duration
      ? `${Math.floor(route.summary.duration / 60)}분`
      : '시간 정보 없음';

    // 기본 레이어 속성 설정
    const layerProps: ManagedLayerProps = {
      id: `route-${toolCallId}`,
      name: `경로 - ${distanceText}`,
      type: "geojson",
      visible: true,
      opacity: 0.8,
      zIndex: 50,
      source: 'tool-result',
      toolCallId,
      style: {
        stroke: {
          color: '#2563eb', // 파란색 경로 라인
          width: 8
        },
        fill: { color: [255, 255, 255, 0.4] }, // 흰색 채우기
      },
      info: {
        lyrId: `route-${toolCallId}`,
        lyrNm: `경로 - ${distanceText}`,
        description: `길찾기 결과 경로 (${durationText})`
      }
    };

    // mapState가 있고 유효한 경로 데이터가 있는 경우 GeoJSON 생성
    if (mapState?.map && route && route.result_code === 0 && route.sections) {
      try {
        const coordinates: GeoJSONCoordinate[] = [];
        const projection = mapState.map.getProjection();

        // 모든 섹션의 도로 정보에서 좌표 추출 및 변환
        route.sections.forEach(section => {
          section.roads?.forEach(road => {
            if (road.vertexes && Array.isArray(road.vertexes) && road.vertexes.length >= 2) {
              // vertexes는 [x1, y1, x2, y2, ...] 형태의 배열
              for (let i = 0; i < road.vertexes.length; i += 2) {
                const lon = road.vertexes[i];
                const lat = road.vertexes[i + 1];

                if (typeof lon === 'number' && typeof lat === 'number') {
                  // 좌표계 변환: EPSG:4326 -> 지도 좌표계
                  const projectedCoord = projection.project([lon, lat], "4326");
                  if (Array.isArray(projectedCoord) && projectedCoord.length >= 2) {
                    coordinates.push([projectedCoord[0], projectedCoord[1]] as GeoJSONCoordinate);
                  }
                }
              }
            }
          });
        });

        // GeoJSON LineString 생성 (타입 안전성 보장)
        if (coordinates.length > 0) {
          const geoJsonData: GeoJSONFeatureCollection = {
            type: "FeatureCollection",
            features: [
              {
                type: "Feature",
                geometry: {
                  type: "LineString",
                  coordinates: coordinates
                },
                properties: {
                  name: "경로",
                  distance: route.summary?.distance || 0,
                  duration: route.summary?.duration || 0,
                  toolCallId
                }
              }
            ]
          };

          layerProps.data = geoJsonData;
        }
      } catch (error) {
        layerProps.data = result;
      }
    } else {
      // mapState가 없거나 유효하지 않은 경우 원본 데이터 사용
      layerProps.data = result;
    }

    return layerProps;
  },
};
