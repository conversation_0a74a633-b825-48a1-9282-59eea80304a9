/**
 * 레이어 관련 도구 사용 규칙 - 간소화된 버전
 * 중복 제거 및 계층적 구조로 재구성
 */

export type LayerAgentType = 'unified' | 'style';

// 핵심 공통 규칙 (중복 제거)
export const CORE_LAYER_RULES = {
  // 현재 지도 상태 확인 (최우선)
  MAP_STATE_CHECK: "모든 작업 전에 Current map state에서 대상 레이어 존재 여부 확인",

  // 레이어 ID 형식 및 확보
  LAYER_ID_FORMAT: "LR로 시작하는 10자리 (예: LR0000003825)",
  LAYER_ID_EXTRACTION: "chooseOption 결과가 '레이어명|레이어ID' 형태면 '|' 뒤의 ID 추출",

  // 중복 방지
  DUPLICATE_PREVENTION: "이미 지도에 있는 레이어는 getLayer 재호출 금지",

  // 작업 순서
  WORK_ORDER: "현재 지도 상태 확인 → 레이어 ID 확보 → 필요시에만 도구 호출",

  // HIL 도구 사용
  HIL_USAGE: "검색 결과 여러개: chooseOption (반드시 '레이어명|레이어ID' 형태), 검색 실패: getUserInput, 중요 작업: confirmWithCheckbox, 사용자 위치 확인: getLocation",

  // 속성 기반 요청 식별 패턴
  ATTRIBUTE_REQUEST_PATTERNS: `
    형용사: "노후화된", "높은", "오래된", "새로운", "큰", "작은", "낡은", "최신"
    필터링: "~만", "~인 것만", "조건에 맞는", "특정 조건"
    색상 지정: "흰색으로", "노란색으로", "빨간색으로", "파란색으로"
    비교: "~보다 높은", "~이상", "~이하", "~년도 이후", "~년도 이전"
    지역 조건: "서울에 있는", "부산에 있는", "강남구의", "용산구만", "경기도에 있는", "~시의", "~구의", "~동의"`
} as const;

// 도구별 특화 규칙 (상세화)
export const TOOL_RULES = {
  getLayerList: "userId='geonuser', layerName='키워드' lyrTySeCode='' (비워둠)",
  getLayer: "현재 지도 상태 확인 후 중복 방지, 레이어 ID 형식 검증",
  getLayerAttributes: `
    - getLayer 선행 필수, 400 에러 시 getLayer 재시도
    - 속성 기반 요청 시 반드시 호출 (노후화, 높이, 크기 등)
    - 조건부 스타일링 전 필수 단계
    - 속성 컬럼 정보를 바탕으로 사용자 조건 매칭
    - 🚨 지역 조건 시 우선순위: 시도명 > 주소 > 구명 > 동명 > 기타 지역 관련 컬럼`,
  updateLayerStyle: "현재 지도 상태 확인, 유형별 스타일링 시 속성 조회 필수",
  removeLayer: "현재 지도 상태 확인, 중요 작업 시 confirmWithCheckbox 사용"
} as const;

// 작업 흐름 템플릿 (상세화)
export const WORKFLOWS = {
  ADD_LAYER: "지도 상태 확인 → ID 확보 → 필요시 검색/선택 → getLayer",
  CHANGE_STYLE: "지도 상태 확인 → 레이어 없으면 추가 → 스타일 적용",
  DELETE_LAYER: "지도 상태 확인 → ID 확인 → 필요시 확인 → removeLayer",

  // 새로운 상세 워크플로우 추가
  LAYER_WITH_ATTRIBUTES: `
    1. getLayerList로 키워드 검색
    2. 검색 결과 처리 (chooseOption/자동선택)
    3. getLayer로 레이어 추가
    4. getLayerAttributes로 속성 정보 조회
    5. 필요시 조건부 스타일링 또는 필터링 적용`,

  CONDITIONAL_STYLING: `
    1. 현재 지도 상태에서 대상 레이어 확인
    2. 레이어가 없으면 LAYER_WITH_ATTRIBUTES 워크플로우 실행
    3. getLayerAttributes로 속성 정보 조회 (필수)
    4. 속성값 기반 조건부 스타일링 적용`,

  ATTRIBUTE_BASED_FILTER: `
    1. 대상 레이어 확보 (없으면 검색/추가)
    2. getLayerAttributes로 속성 컬럼 확인
    3. 사용자 조건에 맞는 속성 컬럼 식별
    4. createLayerFilter 또는 updateLayerStyle로 필터링 적용`
} as const;

// 상세화된 프롬프트 생성 함수
export function createLayerToolRules(): string {
  return `
**🚨 레이어 도구 사용 핵심 규칙:**

**최우선 원칙:**
- ${CORE_LAYER_RULES.MAP_STATE_CHECK}
- ${CORE_LAYER_RULES.WORK_ORDER}
- ${CORE_LAYER_RULES.DUPLICATE_PREVENTION}

**레이어 ID 관리:**
- 형식: ${CORE_LAYER_RULES.LAYER_ID_FORMAT}
- 추출: ${CORE_LAYER_RULES.LAYER_ID_EXTRACTION}

**도구별 핵심 규칙:**
- **getLayerList**: ${TOOL_RULES.getLayerList}
- **getLayer**: ${TOOL_RULES.getLayer}
- **getLayerAttributes**: ${TOOL_RULES.getLayerAttributes}
- **updateLayerStyle**: ${TOOL_RULES.updateLayerStyle}
- **removeLayer**: ${TOOL_RULES.removeLayer}

**🎯 상세 작업 흐름 (필수 준수):**

**기본 레이어 추가**: ${WORKFLOWS.ADD_LAYER}

**속성 정보가 필요한 레이어 작업**: ${WORKFLOWS.LAYER_WITH_ATTRIBUTES}

**조건부 스타일링**: ${WORKFLOWS.CONDITIONAL_STYLING}

**속성 기반 필터링**: ${WORKFLOWS.ATTRIBUTE_BASED_FILTER}

**HIL 도구 활용:**
- ${CORE_LAYER_RULES.HIL_USAGE}

**에러 방지 체크리스트:**
- Current map state 확인했는가?
- 레이어 ID가 LR로 시작하는 10자리인가?
- 중복 추가를 방지했는가?
- 속성 기반 요청 시 getLayerAttributes를 호출했는가?
- HIL 도구를 적절히 사용했는가?
  `.trim();
}

// 에이전트별 맞춤 규칙 생성 함수 (상세화)
export function getLayerToolRulesForAgent(agentType: LayerAgentType): string {
  const baseRules = createLayerToolRules();

  switch (agentType) {
    case 'unified':
      return baseRules + `

**🎯 통합 레이어 에이전트 특화 규칙:**

**핵심 임무:**
- 레이어 검색, 추가, 삭제, 스타일 변경, 속성 조회, 필터링 모든 기능 지원
- 현재 지도 상태 우선 확인으로 효율적 작업 흐름 보장
- HIL 도구 적극 활용으로 사용자 경험 향상

**요청 패턴별 처리 방법:**

**1. "기준이 애매한 필터링 요청" 패턴:**
   → getLayerList("건물")
   → chooseOption으로 적절한 레이어 선택
   → getLayer로 레이어 추가
   → getLayerAttributes로 속성 조회
   → "노후화" 관련 속성 컬럼 식별
   → getUserInput(또는 HIL 도구)으로 사용자에게 기준을 물어보기
   → generateCategoricalStyle로 조건부 스타일링 또는 createLayerFilter로 필터링

**2. "높은 건물만 보여줘" 패턴:**
   → 현재 지도에 건물 레이어 확인
   → 없으면 건물 레이어 검색/추가
   → getLayerAttributes로 높이 관련 속성 확인
   → 높이 조건 기반 필터링/스타일링

**3. 지역명 + 속성 조건 패턴 (🚨 지역 조건 우선 처리 🚨):**
   → 지역명을 키워드로 레이어 검색
   → 해당 지역 레이어 우선 선택
   → 속성 조회 후 **지역 관련 속성 컬럼을 우선적으로 식별**
   → **"서울에 있는 약국만 빨간색으로" = 지역 조건(서울) 기준으로 스타일링**
   → **영업상태가 아닌 지역 속성(시도명, 주소 등)을 attributeName으로 사용**

**필수 실행 순서:**
1. 현재 지도 상태 확인 (중복 방지)
2. 키워드 기반 레이어 검색
3. 레이어 추가 (getLayer)
4. 속성 정보 조회 (getLayerAttributes) - 조건부 요청 시 필수
5. 조건부 스타일링/필터링 적용
      `;

    case 'style':
      return baseRules + `

**🎯 스타일링 특화:**
- 스타일 변경이 주 목적
- 조건부 스타일링 시 속성 정보 필수
- generateCategoricalStyle 사용 시 속성값 검증 필수
      `;

    default:
      return baseRules;
  }
}